// 登录认证功能已屏蔽 - Login authentication functionality disabled
export const AppRoutes = [
  {
    path: '/',
    name: 'Home',
    meta: {
      requiresAuth: false // 已屏蔽认证要求 - Authentication requirement disabled
    },
    component: () => import('@/views/index.vue')
  },
  // 连接管理路由
  {
    path: '/connections/active',
    name: 'ActiveConnections',
    meta: {
      requiresAuth: false,
      title: '活动连接管理'
    },
    component: () => import('@/views/connections/ConnectionManagement.vue')
  },
  // SMB管理路由
  {
    path: '/smb/management',
    name: 'SMBManagement',
    meta: {
      requiresAuth: false,
      title: 'SMB管理工具'
    },
    component: () => import('@/views/smb/SMBManagement.vue')
  },
  {
    path: '/connections/history',
    name: 'ConnectionHistory',
    meta: {
      requiresAuth: false,
      title: '连接历史记录'
    },
    component: () => import('@/views/connections/ConnectionManagement.vue')
  },
  {
    path: '/settings/connections',
    name: 'ConnectionSettings',
    meta: {
      requiresAuth: false,
      title: '连接配置管理'
    },
    component: () => import('@/views/settings/GlobalSettings.vue')
  },
  {
    path: '/test/console-rdp',
    name: 'TestConsoleRdp',
    component: () => import('@/views/test-console-rdp.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/optimization-demo',
    name: 'OptimizationDemo',
    meta: {
      requiresAuth: false
    },
    component: () => import('@/views/OptimizationDemo.vue')
  },
  {
    path: '/test-enterprise',
    name: 'TestEnterprise',
    meta: {
      requiresAuth: false,
      title: '企业资源管理测试'
    },
    component: () => import('@/views/test-enterprise.vue')
  },
  // 企业资源管理路由
  {
    path: '/resources/personal',
    name: 'PersonalResources',
    meta: {
      requiresAuth: false,
      title: '个人资源管理'
    },
    component: () => import('@/views/resources/PersonalResourceManagement.vue')
  },
  {
    path: '/resources/enterprise',
    name: 'EnterpriseResources',
    meta: {
      requiresAuth: false,
      title: '企业资源管理'
    },
    component: () => import('@/views/enterprise/EnterpriseResourceManagement.vue')
  },
  // 固定资产管理路由
  {
    path: '/enterprise/asset-management',
    name: 'AssetManagement',
    meta: {
      requiresAuth: false,
      title: '固定资产管理'
    },
    component: () => import('@/views/enterprise/AssetManagement.vue')
  },
  // 机房管理路由
  {
    path: '/enterprise/room-management',
    name: 'RoomManagement',
    meta: {
      requiresAuth: false,
      title: '机房管理'
    },
    component: () => import('@/views/enterprise/RoomManagement.vue')
  },
  {
    path: '/enterprise/room-management/realtime',
    name: 'RoomRealtimeMonitor',
    meta: {
      requiresAuth: false,
      title: '机房实时监控'
    },
    component: () => import('@/views/enterprise/RoomRealtimeMonitor.vue')
  },
  {
    path: '/enterprise/room-management/analysis',
    name: 'RoomDataAnalysis',
    meta: {
      requiresAuth: false,
      title: '机房数据分析'
    },
    component: () => import('@/views/enterprise/RoomDataAnalysis.vue')
  },
  {
    path: '/enterprise/room-management/config',
    name: 'RoomConfigManagement',
    meta: {
      requiresAuth: false,
      title: '机房配置管理'
    },
    component: () => import('@/views/enterprise/RoomConfigManagement.vue')
  },
  // 新增：机柜布局与机柜详情
  {
    path: '/enterprise/room-management/layout/:roomId',
    name: 'RoomRackLayout',
    meta: {
      requiresAuth: false,
      title: '机柜布局'
    },
    component: () => import('@/views/enterprise/RoomRackLayout.vue')
  },
  {
    path: '/enterprise/room-management/rack/:roomId/:rackId',
    name: 'RackDetail',
    meta: {
      requiresAuth: false,
      title: '机柜详情'
    },
    component: () => import('@/views/enterprise/RackDetail.vue')
  },
  // 同步配置路由
  {
    path: '/sync/config',
    name: 'SyncConfig',
    meta: {
      requiresAuth: false,
      title: '同步配置'
    },
    component: () => import('@/views/sync/SyncConfiguration.vue')
  },
  {
    path: '/sync/monitor',
    name: 'SyncMonitor',
    meta: {
      requiresAuth: false,
      title: '监控仪表板'
    },
    component: () => import('@/views/sync/SyncMonitor.vue')
  },
  // 安全管理路由
  {
    path: '/security/permissions',
    name: 'SecurityPermissions',
    meta: {
      requiresAuth: false,
      title: '权限管理'
    },
    component: () => import('@/views/security/PermissionManagement.vue')
  },
  {
    path: '/security/audit',
    name: 'SecurityAudit',
    meta: {
      requiresAuth: false,
      title: '审计日志'
    },
    component: () => import('@/views/security/AuditLog.vue')
  },
  // 系统设置路由
  {
    path: '/settings/global',
    name: 'GlobalSettings',
    meta: {
      requiresAuth: false,
      title: '全局设置'
    },
    component: () => import('@/views/settings/GlobalSettings.vue')
  },
  {
    path: '/settings/users',
    name: 'UserManagement',
    meta: {
      requiresAuth: false,
      title: '用户管理'
    },
    component: () => import('@/views/settings/UserManagement.vue')
  }
  // 登录路由已移除 - Login route removed
  // {
  //   path: '/login',
  //   name: 'Login',
  //   meta: {
  //     requiresAuth: false
  //   },
  //   component: () => import('@/views/auth/login.vue')
  // }
]
