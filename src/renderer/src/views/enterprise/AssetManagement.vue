<template>
  <div class="asset-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Package class="title-icon" />
            固定资产管理
          </h1>
          <p class="page-description">管理企业固定资产、设备清单和资产状态跟踪</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          :disabled="loading"
          @click="refreshAssets"
        >
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新
        </button>
        <button
          class="btn btn-secondary"
          @click="exportAssets"
        >
          <Download class="btn-icon" />
          导出
        </button>
        <button
          class="btn btn-secondary"
          @click="showImportDialog = true"
        >
          <Upload class="btn-icon" />
          导入
        </button>
        <button
          class="btn btn-secondary"
          @click="showReportDialog = true"
        >
          <BarChart3 class="btn-icon" />
          报表
        </button>
        <button
          v-if="selectedAssets.length > 0"
          class="btn btn-danger"
          @click="batchDelete"
        >
          <Trash2 class="btn-icon" />
          批量删除 ({{ selectedAssets.length }})
        </button>
        <button
          class="btn btn-primary"
          @click="showAddAssetDialog = true"
        >
          <Plus class="btn-icon" />
          新增资产
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <Package class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalAssets }}</div>
          <div class="stat-label">总资产数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <CheckCircle class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ activeAssets }}</div>
          <div class="stat-label">在用资产</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon maintenance">
          <Wrench class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ maintenanceAssets }}</div>
          <div class="stat-label">维护中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon retired">
          <XCircle class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ retiredAssets }}</div>
          <div class="stat-label">已报废</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="search-box">
        <Search class="search-icon" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索资产名称、编号或型号..."
          class="search-input"
        />
      </div>
      <div class="filter-controls">
        <select
          v-model="selectedCategory"
          class="filter-select"
        >
          <option value="">全部分类</option>
          <option value="computer">计算机设备</option>
          <option value="network">网络设备</option>
          <option value="server">服务器</option>
          <option value="storage">存储设备</option>
          <option value="printer">打印设备</option>
          <option value="furniture">办公家具</option>
          <option value="other">其他设备</option>
        </select>
        <select
          v-model="selectedStatus"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="active">在用</option>
          <option value="maintenance">维护中</option>
          <option value="idle">闲置</option>
          <option value="retired">已报废</option>
        </select>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="asset-list">
      <div class="list-header">
        <div class="header-cell">
          <input
            type="checkbox"
            :checked="selectedAssets.length === filteredAssets.length && filteredAssets.length > 0"
            :indeterminate="selectedAssets.length > 0 && selectedAssets.length < filteredAssets.length"
            @change="toggleSelectAll"
          />
        </div>
        <div class="header-cell">资产编号</div>
        <div class="header-cell">资产名称</div>
        <div class="header-cell">分类</div>
        <div class="header-cell">型号</div>
        <div class="header-cell">状态</div>
        <div class="header-cell">使用人</div>
        <div class="header-cell">购买日期</div>
        <div class="header-cell">操作</div>
      </div>
      <div class="list-body">
        <div
          v-for="asset in filteredAssets"
          :key="asset.id"
          class="list-row"
        >
          <div class="cell">
            <input
              v-model="selectedAssets"
              type="checkbox"
              :value="asset.id"
            />
          </div>
          <div class="cell">{{ asset.assetNumber }}</div>
          <div class="cell">{{ asset.name }}</div>
          <div class="cell">
            <span
              class="category-tag"
              :class="asset.category"
            >
              {{ getCategoryLabel(asset.category) }}
            </span>
          </div>
          <div class="cell">{{ asset.model }}</div>
          <div class="cell">
            <span
              class="status-badge"
              :class="asset.status"
            >
              {{ getStatusLabel(asset.status) }}
            </span>
          </div>
          <div class="cell">{{ asset.assignedTo || '-' }}</div>
          <div class="cell">{{ formatDate(asset.purchaseDate) }}</div>
          <div class="cell">
            <div class="action-buttons">
              <button
                class="btn-icon-small"
                title="编辑"
                @click="editAsset(asset)"
              >
                <Edit class="icon" />
              </button>
              <button
                class="btn-icon-small"
                title="详情"
                @click="viewAssetDetails(asset)"
              >
                <Eye class="icon" />
              </button>
              <button
                class="btn-icon-small danger"
                title="删除"
                @click="deleteAsset(asset)"
              >
                <Trash2 class="icon" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入资产对话框 -->
    <div
      v-if="showImportDialog"
      class="modal-overlay"
      @click="closeImportDialog"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">导入资产数据</h2>
          <button
            class="modal-close"
            @click="closeImportDialog"
          >
            <X class="close-icon" />
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">选择文件</label>
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              class="form-input"
              @change="handleFileSelect"
            />
            <p class="text-sm text-gray-600 mt-2">
              支持 CSV、Excel 格式文件。请确保文件包含：资产编号、资产名称、分类、状态、使用人、购买价格、购买日期等字段。
            </p>
          </div>
          <div
            v-if="importPreview.length > 0"
            class="import-preview"
          >
            <h4>预览数据 (前5条)</h4>
            <div class="preview-table">
              <div class="preview-header">
                <span>资产编号</span>
                <span>资产名称</span>
                <span>分类</span>
                <span>状态</span>
              </div>
              <div
                v-for="(item, index) in importPreview.slice(0, 5)"
                :key="index"
                class="preview-row"
              >
                <span>{{ item.assetNumber }}</span>
                <span>{{ item.name }}</span>
                <span>{{ item.category }}</span>
                <span>{{ item.status }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeImportDialog"
            >取消</button
          >
          <button
            :disabled="importPreview.length === 0 || importing"
            class="btn btn-primary"
            @click="importAssets"
          >
            <Loader2
              v-if="importing"
              class="btn-icon animate-spin"
            />
            {{ importing ? '导入中...' : `导入 ${importPreview.length} 条数据` }}
          </button>
        </div>
      </div>
    </div>

    <!-- 新增/编辑资产对话框 -->
    <div
      v-if="showAddAssetDialog || editingAsset"
      class="modal-overlay"
      @click="closeAssetDialog"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">
            {{ editingAsset ? '编辑资产' : '新增资产' }}
          </h2>
          <button
            class="modal-close"
            @click="closeAssetDialog"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="saveAsset">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">资产编号</label>
                <input
                  v-model="assetForm.assetNumber"
                  type="text"
                  class="form-input"
                  placeholder="请输入资产编号"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">资产名称</label>
                <input
                  v-model="assetForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入资产名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">分类</label>
                <select
                  v-model="assetForm.category"
                  class="form-select"
                  required
                >
                  <option value="">请选择分类</option>
                  <option value="computer">计算机设备</option>
                  <option value="network">网络设备</option>
                  <option value="server">服务器</option>
                  <option value="storage">存储设备</option>
                  <option value="printer">打印设备</option>
                  <option value="furniture">办公家具</option>
                  <option value="other">其他设备</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">型号</label>
                <input
                  v-model="assetForm.model"
                  type="text"
                  class="form-input"
                  placeholder="请输入设备型号"
                />
              </div>
              <div class="form-group">
                <label class="form-label">状态</label>
                <select
                  v-model="assetForm.status"
                  class="form-select"
                  required
                >
                  <option value="active">在用</option>
                  <option value="maintenance">维护中</option>
                  <option value="idle">闲置</option>
                  <option value="retired">已报废</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">使用人</label>
                <input
                  v-model="assetForm.assignedTo"
                  type="text"
                  class="form-input"
                  placeholder="请输入使用人姓名"
                />
              </div>
              <div class="form-group">
                <label class="form-label">购买日期</label>
                <input
                  v-model="assetForm.purchaseDate"
                  type="date"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label class="form-label">购买价格</label>
                <input
                  v-model="assetForm.purchasePrice"
                  type="number"
                  class="form-input"
                  placeholder="请输入购买价格"
                  step="0.01"
                />
              </div>
            </div>
            <div class="form-group full-width">
              <label class="form-label">备注</label>
              <textarea
                v-model="assetForm.description"
                class="form-textarea"
                placeholder="请输入备注信息"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeAssetDialog"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            :disabled="saving"
            @click="saveAsset"
          >
            <Loader2
              v-if="saving"
              class="btn-icon animate-spin"
            />
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 资产报表对话框 -->
    <div
      v-if="showReportDialog"
      class="modal-overlay"
      @click="closeReportDialog"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">资产报表</h2>
          <button
            class="modal-close"
            @click="closeReportDialog"
          >
            <X class="close-icon" />
          </button>
        </div>
        <div class="modal-body">
          <div class="report-section">
            <h3>资产统计概览</h3>
            <div class="report-stats">
              <div class="report-stat-item">
                <span class="stat-name">总资产数量：</span>
                <span class="stat-value">{{ totalAssets }}</span>
              </div>
              <div class="report-stat-item">
                <span class="stat-name">在用资产：</span>
                <span class="stat-value">{{ activeAssets }}</span>
              </div>
              <div class="report-stat-item">
                <span class="stat-name">维护中资产：</span>
                <span class="stat-value">{{ maintenanceAssets }}</span>
              </div>
              <div class="report-stat-item">
                <span class="stat-name">已报废资产：</span>
                <span class="stat-value">{{ retiredAssets }}</span>
              </div>
            </div>
          </div>

          <div class="report-section">
            <h3>分类统计</h3>
            <div class="category-stats">
              <div
                v-for="(count, category) in categoryStats"
                :key="category"
                class="category-stat-item"
              >
                <span class="category-name">{{ getCategoryLabel(category) }}：</span>
                <span class="category-count">{{ count }}</span>
              </div>
            </div>
          </div>

          <div class="report-section">
            <h3>资产价值统计</h3>
            <div class="value-stats">
              <div class="value-stat-item">
                <span class="stat-name">总资产价值：</span>
                <span class="stat-value">¥{{ totalValue.toLocaleString() }}</span>
              </div>
              <div class="value-stat-item">
                <span class="stat-name">在用资产价值：</span>
                <span class="stat-value">¥{{ activeValue.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="exportReport"
          >
            <Download class="btn-icon" />
            导出报表
          </button>
          <button
            class="btn btn-primary"
            @click="closeReportDialog"
            >关闭</button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Package,
  RefreshCw,
  Plus,
  CheckCircle,
  Wrench,
  XCircle,
  Search,
  Edit,
  Eye,
  Trash2,
  X,
  Loader2,
  Download,
  Upload,
  BarChart3,
  ArrowLeft
} from 'lucide-vue-next'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const selectedAssets = ref([])
const showAddAssetDialog = ref(false)
const showImportDialog = ref(false)
const showReportDialog = ref(false)
const editingAsset = ref(null)
const importing = ref(false)
const importPreview = ref([])

// 资产表单数据
const assetForm = ref({
  assetNumber: '',
  name: '',
  category: '',
  model: '',
  status: 'active',
  assignedTo: '',
  purchaseDate: '',
  purchasePrice: '',
  description: ''
})

// 模拟资产数据
const assets = ref([
  {
    id: 1,
    assetNumber: 'IT-001',
    name: 'Dell OptiPlex 7090',
    category: 'computer',
    model: 'OptiPlex 7090',
    status: 'active',
    assignedTo: '张三',
    purchaseDate: '2023-01-15',
    purchasePrice: 5999.0,
    description: '办公用台式机'
  },
  {
    id: 2,
    assetNumber: 'NET-001',
    name: 'Cisco 交换机',
    category: 'network',
    model: 'Catalyst 2960',
    status: 'active',
    assignedTo: '',
    purchaseDate: '2023-02-20',
    purchasePrice: 3500.0,
    description: '24口千兆交换机'
  },
  {
    id: 3,
    assetNumber: 'SRV-001',
    name: 'HP ProLiant 服务器',
    category: 'server',
    model: 'DL380 Gen10',
    status: 'maintenance',
    assignedTo: '',
    purchaseDate: '2022-12-10',
    purchasePrice: 25000.0,
    description: '数据库服务器'
  }
])

// 计算属性
const totalAssets = computed(() => assets.value.length)
const activeAssets = computed(() => assets.value.filter((asset) => asset.status === 'active').length)
const maintenanceAssets = computed(() => assets.value.filter((asset) => asset.status === 'maintenance').length)
const retiredAssets = computed(() => assets.value.filter((asset) => asset.status === 'retired').length)

const filteredAssets = computed(() => {
  let filtered = assets.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (asset) =>
        asset.name.toLowerCase().includes(query) || asset.assetNumber.toLowerCase().includes(query) || asset.model.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter((asset) => asset.category === selectedCategory.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter((asset) => asset.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const refreshAssets = async () => {
  loading.value = true
  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 1000))
  loading.value = false
}

const getCategoryLabel = (category: string) => {
  const labels = {
    computer: '计算机设备',
    network: '网络设备',
    server: '服务器',
    storage: '存储设备',
    printer: '打印设备',
    furniture: '办公家具',
    other: '其他设备'
  }
  return labels[category] || category
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '在用',
    maintenance: '维护中',
    idle: '闲置',
    retired: '已报废'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const editAsset = (asset) => {
  editingAsset.value = asset
  assetForm.value = { ...asset }
}

const viewAssetDetails = (asset) => {
  // 实现查看详情逻辑
  console.log('查看资产详情:', asset)
}

const deleteAsset = async (asset) => {
  if (confirm(`确定要删除资产 "${asset.name}" 吗？`)) {
    const index = assets.value.findIndex((a) => a.id === asset.id)
    if (index > -1) {
      assets.value.splice(index, 1)
    }
  }
}

const closeAssetDialog = () => {
  showAddAssetDialog.value = false
  editingAsset.value = null
  resetAssetForm()
}

const resetAssetForm = () => {
  assetForm.value = {
    assetNumber: '',
    name: '',
    category: '',
    model: '',
    status: 'active',
    assignedTo: '',
    purchaseDate: '',
    purchasePrice: '',
    description: ''
  }
}

const toggleSelectAll = () => {
  if (selectedAssets.value.length === filteredAssets.value.length) {
    selectedAssets.value = []
  } else {
    selectedAssets.value = filteredAssets.value.map((asset) => asset.id)
  }
}

const batchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedAssets.value.length} 个资产吗？`)) {
    assets.value = assets.value.filter((asset) => !selectedAssets.value.includes(asset.id))
    selectedAssets.value = []
  }
}

const exportAssets = () => {
  // 实现导出功能
  const csvContent = generateCSV(filteredAssets.value)
  downloadCSV(csvContent, 'assets.csv')
}

const generateCSV = (data) => {
  const headers = ['资产编号', '资产名称', '分类', '型号', '状态', '使用人', '购买日期', '购买价格']
  const rows = data.map((asset) => [
    asset.assetNumber,
    asset.name,
    getCategoryLabel(asset.category),
    asset.model,
    getStatusLabel(asset.status),
    asset.assignedTo || '',
    formatDate(asset.purchaseDate),
    asset.purchasePrice
  ])

  return [headers, ...rows].map((row) => row.join(',')).join('\n')
}

const downloadCSV = (content, filename) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

const closeImportDialog = () => {
  showImportDialog.value = false
  importPreview.value = []
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const text = e.target.result as string
      const lines = text.split('\n')
      const headers = lines[0].split(',')

      const preview = lines
        .slice(1, 6)
        .map((line) => {
          const values = line.split(',')
          return {
            assetNumber: values[0] || '',
            name: values[1] || '',
            category: values[2] || '',
            status: values[3] || ''
          }
        })
        .filter((item) => item.assetNumber)

      importPreview.value = preview
    } catch (error) {
      console.error('文件解析失败:', error)
    }
  }
  reader.readAsText(file)
}

const importAssets = async () => {
  importing.value = true

  try {
    // 模拟导入过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 这里应该是实际的导入逻辑
    const newAssets = importPreview.value.map((item) => ({
      ...item,
      id: Date.now() + Math.random(),
      model: '',
      assignedTo: '',
      purchaseDate: '',
      purchasePrice: 0,
      description: ''
    }))

    assets.value.push(...newAssets)
    closeImportDialog()
  } catch (error) {
    console.error('导入失败:', error)
  } finally {
    importing.value = false
  }
}

const saveAsset = async () => {
  saving.value = true

  try {
    if (editingAsset.value) {
      // 编辑现有资产
      const index = assets.value.findIndex((a) => a.id === editingAsset.value.id)
      if (index > -1) {
        assets.value[index] = {
          ...assetForm.value,
          id: editingAsset.value.id,
          purchasePrice: parseFloat(assetForm.value.purchasePrice) || 0
        }
      }
    } else {
      // 新增资产
      const newAsset = {
        ...assetForm.value,
        id: Date.now(), // 简单的ID生成
        purchasePrice: parseFloat(assetForm.value.purchasePrice) || 0
      }
      assets.value.push(newAsset)
    }

    closeAssetDialog()
  } catch (error) {
    console.error('保存资产失败:', error)
  } finally {
    saving.value = false
  }
}

const closeReportDialog = () => {
  showReportDialog.value = false
}

const categoryStats = computed(() => {
  const stats = {}
  assets.value.forEach((asset) => {
    stats[asset.category] = (stats[asset.category] || 0) + 1
  })
  return stats
})

const totalValue = computed(() => {
  return assets.value.reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0)
})

const activeValue = computed(() => {
  return assets.value.filter((asset) => asset.status === 'active').reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0)
})

const exportReport = () => {
  const reportData = {
    统计时间: new Date().toLocaleString('zh-CN'),
    总资产数量: totalAssets.value,
    在用资产: activeAssets.value,
    维护中资产: maintenanceAssets.value,
    已报废资产: retiredAssets.value,
    总资产价值: totalValue.value,
    在用资产价值: activeValue.value,
    分类统计: Object.entries(categoryStats.value).map(([category, count]) => ({
      分类: getCategoryLabel(category),
      数量: count
    }))
  }

  const jsonStr = JSON.stringify(reportData, null, 2)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `资产报表_${new Date().toISOString().split('T')[0]}.json`
  link.click()
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  refreshAssets()
})
</script>

<style scoped>
/* 基础布局 */
.asset-management {
  min-height: 100vh;
  background: #f8fafc;
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: #3b82f6;
}

.page-description {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

/* 返回按钮 */
.btn-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.btn-back:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.btn-back:active {
  background: #f1f5f9;
  transform: translateY(1px);
}

.btn-back .btn-icon {
  width: 18px;
  height: 18px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
}

.stat-icon.active {
  background: #dcfce7;
}

.stat-icon.maintenance {
  background: #fef3c7;
}

.stat-icon.retired {
  background: #fee2e2;
}

.stat-icon .icon {
  width: 24px;
  height: 24px;
  color: #64748b;
}

.stat-icon.active .icon {
  color: #16a34a;
}

.stat-icon.maintenance .icon {
  color: #d97706;
}

.stat-icon.retired .icon {
  color: #dc2626;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

/* 筛选区域 */
.filter-section {
  padding: 0 24px 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #64748b;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

/* 资产列表 */
.asset-list {
  margin: 0 24px 24px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.list-header {
  display: grid;
  grid-template-columns: 50px 120px 200px 120px 150px 100px 120px 120px 120px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-cell {
  padding: 12px 16px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-right: 1px solid #e2e8f0;
}

.header-cell:last-child {
  border-right: none;
}

.list-body {
  max-height: 600px;
  overflow-y: auto;
}

.list-row {
  display: grid;
  grid-template-columns: 50px 120px 200px 120px 150px 100px 120px 120px 120px;
  border-bottom: 1px solid #f1f5f9;
}

.list-row:hover {
  background: #f8fafc;
}

.cell {
  padding: 12px 16px;
  font-size: 14px;
  color: #374151;
  border-right: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
}

.cell:last-child {
  border-right: none;
}

.category-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #f1f5f9;
  color: #64748b;
}

.category-tag.computer {
  background: #dbeafe;
  color: #1d4ed8;
}

.category-tag.network {
  background: #dcfce7;
  color: #16a34a;
}

.category-tag.server {
  background: #fef3c7;
  color: #d97706;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.maintenance {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.idle {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge.retired {
  background: #fee2e2;
  color: #dc2626;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.btn-icon-small {
  width: 28px;
  height: 28px;
  border: none;
  background: #f8fafc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon-small:hover {
  background: #e2e8f0;
}

.btn-icon-small.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

.btn-icon-small .icon {
  width: 14px;
  height: 14px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f8fafc;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f1f5f9;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f8fafc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 导入预览样式 */
.import-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
}

.import-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.preview-table {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background: #f1f5f9;
  font-weight: 600;
  font-size: 12px;
  color: #374151;
}

.preview-header span,
.preview-row span {
  padding: 8px 12px;
  border-right: 1px solid #e2e8f0;
}

.preview-header span:last-child,
.preview-row span:last-child {
  border-right: none;
}

.preview-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background: white;
  font-size: 12px;
  color: #64748b;
  border-bottom: 1px solid #f1f5f9;
}

.preview-row:last-child {
  border-bottom: none;
}

.text-sm {
  font-size: 12px;
}

.text-gray-600 {
  color: #64748b;
}

.mt-2 {
  margin-top: 8px;
}

/* 报表样式 */
.report-section {
  margin-bottom: 24px;
}

.report-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.report-stats,
.category-stats,
.value-stats {
  display: grid;
  gap: 8px;
}

.report-stat-item,
.category-stat-item,
.value-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 4px;
}

.stat-name,
.category-name {
  font-size: 14px;
  color: #64748b;
}

.stat-value,
.category-count {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
