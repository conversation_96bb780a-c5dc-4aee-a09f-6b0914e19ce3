<template>
  <div class="room-management">
    <!-- 现代化侧边栏导航 -->
    <aside
      class="sidebar"
      :class="{ collapsed: !sidebarExpanded }"
    >
      <!-- 侧边栏头部 -->
      <header class="sidebar-header">
        <div class="brand">
          <div class="brand-logo">
            <Building class="logo-icon" />
          </div>
          <div
            v-show="sidebarExpanded"
            class="brand-text"
          >
            <h1 class="brand-title">机房中心</h1>
            <span class="brand-subtitle">DataCenter Hub</span>
          </div>
        </div>
        <button
          class="sidebar-toggle"
          :aria-label="sidebarExpanded ? '收起侧边栏' : '展开侧边栏'"
          @click="toggleSidebar"
        >
          <ChevronLeft
            v-if="sidebarExpanded"
            class="toggle-icon"
          />
          <ChevronRight
            v-else
            class="toggle-icon"
          />
        </button>
      </header>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <h3 class="nav-section-title">主要功能</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <a
                href="#"
                class="nav-link active"
                @click.prevent="goOverview"
              >
                <Grid class="nav-icon" />
                <span class="nav-text">总览</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="#"
                class="nav-link"
                @click.prevent="goRealtimeMonitor"
              >
                <Monitor class="nav-icon" />
                <span class="nav-text">实时监控</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="#"
                class="nav-link"
                @click.prevent="goDataAnalysis"
              >
                <BarChart3 class="nav-icon" />
                <span class="nav-text">数据分析</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="#"
                class="nav-link"
                @click.prevent="goConfigManagement"
              >
                <Settings class="nav-icon" />
                <span class="nav-text">配置管理</span>
              </a>
            </li>
          </ul>
        </div>

        <!-- 筛选器 -->
        <div class="nav-section">
          <h3 class="nav-section-title">筛选条件</h3>
          <div class="filter-group">
            <div class="filter-item">
              <label class="filter-label">状态</label>
              <div class="filter-options">
                <label class="checkbox-item">
                  <input
                    v-model="filters.normal"
                    type="checkbox"
                  />
                  <span class="checkbox-mark"></span>
                  <span class="checkbox-text">正常</span>
                </label>
                <label class="checkbox-item">
                  <input
                    v-model="filters.warning"
                    type="checkbox"
                  />
                  <span class="checkbox-mark"></span>
                  <span class="checkbox-text">告警</span>
                </label>
                <label class="checkbox-item">
                  <input
                    v-model="filters.critical"
                    type="checkbox"
                  />
                  <span class="checkbox-mark"></span>
                  <span class="checkbox-text">故障</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main
      class="main-container"
      :class="{ 'sidebar-collapsed': !sidebarExpanded }"
    >
      <!-- 页面头部 -->
      <header class="page-header">
        <!-- 面包屑导航 -->
        <nav class="breadcrumb-nav">
          <button
            class="breadcrumb-item"
            @click="goBack"
          >
            <ArrowLeft class="breadcrumb-icon" />
            <span>企业管理</span>
          </button>
          <ChevronRight class="breadcrumb-separator" />
          <span class="breadcrumb-current">
            <Building class="breadcrumb-icon" />
            机房管理
          </span>
        </nav>

        <!-- 主标题区域 -->
        <div class="title-section">
          <div class="title-group">
            <h1 class="page-title">机房管理中心</h1>
            <p class="page-subtitle">实时监控 · 环境管理 · 容量规划 · 运维分析</p>
          </div>

          <!-- 快速操作工具栏 -->
          <div class="toolbar">
            <div class="toolbar-group">
              <button
                class="toolbar-btn"
                title="视图切换"
                @click="toggleViewMode"
              >
                <Grid class="toolbar-icon" />
              </button>
              <button
                class="toolbar-btn"
                title="筛选"
                @click="toggleFilterPanel"
              >
                <Filter class="toolbar-icon" />
              </button>
              <button
                class="toolbar-btn"
                :class="{ active: batchMode }"
                title="批量操作"
                @click="toggleBatchMode"
              >
                <CheckCircle class="toolbar-icon" />
              </button>
              <button
                class="toolbar-btn"
                title="导出"
                @click="exportRooms"
              >
                <Download class="toolbar-icon" />
              </button>
            </div>

            <div class="toolbar-divider"></div>

            <div class="toolbar-group">
              <button
                class="toolbar-btn"
                :disabled="loading"
                title="刷新数据"
                @click="refreshRooms"
              >
                <RefreshCw
                  class="toolbar-icon"
                  :class="{ 'animate-spin': loading }"
                />
              </button>
              <button
                class="btn btn-primary"
                :disabled="loading"
                @click="showAddRoomDialog = true"
              >
                <Plus
                  v-if="!loading"
                  class="btn-icon"
                />
                <div
                  v-else
                  class="loading-spinner"
                ></div>
                {{ loading ? '加载中...' : '新建机房' }}
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <section class="main-content">
        <!-- 统计仪表板 -->
        <div class="dashboard">
          <!-- 主要统计卡片 -->
          <div class="stats-overview">
            <!-- 状态统计网格 -->
            <div class="stats-grid">
              <div class="stat-card success">
                <div class="stat-header">
                  <CheckCircle class="stat-icon" />
                  <div class="stat-trend">
                    <span class="trend-value">+5%</span>
                  </div>
                </div>
                <div class="stat-body">
                  <div class="stat-value">{{ normalRooms }}</div>
                  <div class="stat-label">正常运行</div>
                  <div class="stat-detail">运行时长 99.9%</div>
                </div>
              </div>

              <div class="stat-card warning">
                <div class="stat-header">
                  <AlertTriangle class="stat-icon" />
                  <div class="stat-trend">
                    <span class="trend-value">-2%</span>
                  </div>
                </div>
                <div class="stat-body">
                  <div class="stat-value">{{ warningRooms }}</div>
                  <div class="stat-label">告警中</div>
                  <div class="stat-detail">温度/湿度异常</div>
                </div>
              </div>

              <div class="stat-card danger">
                <div class="stat-header">
                  <XCircle class="stat-icon" />
                  <div class="stat-trend">
                    <span class="trend-value">0</span>
                  </div>
                </div>
                <div class="stat-body">
                  <div class="stat-value">{{ criticalRooms }}</div>
                  <div class="stat-label">故障</div>
                  <div class="stat-detail">设备离线</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 机房列表 -->
        <div class="rooms-section">
          <div class="section-header">
            <div class="section-title">
              <h2>机房列表</h2>
              <span class="room-count">共 {{ totalRooms }} 个机房</span>
            </div>
            <div class="section-actions">
              <div class="search-box">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索机房名称或位置..."
                  class="search-input"
                />
              </div>
              <div class="filter-dropdown">
                <button class="filter-btn">
                  <Filter class="icon" />
                  筛选
                </button>
              </div>
            </div>
          </div>

          <!-- 机房表格 -->
          <div class="rooms-table">
            <div class="table-header">
              <div class="header-cell room-info-col">机房信息</div>
              <div class="header-cell status-col">状态</div>
              <div class="header-cell metrics-col">环境监控</div>
              <div class="header-cell utilization-col">资源利用率</div>
              <div class="header-cell actions-col">操作</div>
            </div>

            <div class="table-body">
              <div
                v-for="room in filteredRooms"
                :key="room.id"
                class="room-row"
                :class="[room.status, { 'room-loading': room.loading }]"
                @click="viewRoomDetails(room)"
              >
                <!-- 机房信息列 -->
                <div class="table-cell room-info-cell">
                  <div class="room-avatar">
                    <Building class="avatar-icon" />
                  </div>
                  <div class="room-details">
                    <h3 class="room-name">{{ room.name }}</h3>
                    <p class="room-location">{{ room.location }}</p>
                    <div class="room-meta">
                      <span class="meta-item">{{ room.area }}m²</span>
                      <span class="meta-separator">·</span>
                      <span class="meta-item">{{ room.rackCount }}机柜</span>
                    </div>
                  </div>
                </div>

                <!-- 状态列 -->
                <div class="table-cell status-cell">
                  <div
                    class="status-badge"
                    :class="room.status"
                  >
                    <div class="status-dot"></div>
                    <span class="status-text">{{ getStatusText(room.status) }}</span>
                  </div>
                  <div class="status-detail">{{ room.statusDetail }}</div>
                </div>

                <!-- 环境监控列 -->
                <div class="table-cell metrics-cell">
                  <div class="metric-item">
                    <Thermometer class="metric-icon" />
                    <span class="metric-value">{{ room.temperature }}°C</span>
                  </div>
                  <div class="metric-item">
                    <Droplets class="metric-icon" />
                    <span class="metric-value">{{ room.humidity }}%</span>
                  </div>
                </div>

                <!-- 资源利用率列 -->
                <div class="table-cell utilization-cell">
                  <div class="utilization-item">
                    <span class="utilization-label">机柜</span>
                    <div class="utilization-bar">
                      <div
                        class="utilization-fill"
                        :style="{ width: room.rackUtilization + '%' }"
                      ></div>
                    </div>
                    <span class="utilization-value">{{ room.rackUtilization }}%</span>
                  </div>
                  <div class="utilization-item">
                    <span class="utilization-label">电力</span>
                    <div class="utilization-bar">
                      <div
                        class="utilization-fill"
                        :style="{ width: room.powerUtilization + '%' }"
                      ></div>
                    </div>
                    <span class="utilization-value">{{ room.powerUtilization }}%</span>
                  </div>
                </div>

                <!-- 操作列 -->
                <div class="table-cell actions-cell">
                  <div class="action-buttons">
                    <button
                      class="action-btn"
                      title="查看详情"
                      @click.stop="viewRoomDetails(room)"
                    >
                      <Eye class="action-icon" />
                    </button>
                    <button
                      class="action-btn"
                      title="布局"
                      @click.stop="goRackLayout(room)"
                    >
                      <Layout class="action-icon" />
                    </button>
                    <button
                      class="action-btn"
                      title="编辑"
                      :disabled="room.loading"
                      @click.stop="editRoom(room)"
                    >
                      <div
                        v-if="room.loading"
                        class="loading-spinner small"
                      ></div>
                      <Edit
                        v-else
                        class="action-icon"
                      />
                    </button>
                    <button
                      class="action-btn danger"
                      title="删除"
                      @click.stop="deleteRoom(room)"
                    >
                      <Trash2 class="action-icon" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      width="520"
      title="机房详情"
      placement="right"
    >
      <div
        v-if="selectedRoom"
        class="drawer-body"
      >
        <div class="drawer-row"><strong>名称：</strong>{{ selectedRoom.name }}</div>
        <div class="drawer-row"><strong>位置：</strong>{{ selectedRoom.location }}</div>
        <div class="drawer-row"><strong>面积：</strong>{{ selectedRoom.area }} m²</div>
        <div class="drawer-row"><strong>机柜数：</strong>{{ selectedRoom.rackCount }}</div>
        <div class="drawer-row"><strong>状态：</strong>{{ getStatusText(selectedRoom.status) }}</div>
        <div class="drawer-row"><strong>环境：</strong>{{ selectedRoom.temperature }}°C / {{ selectedRoom.humidity }}%</div>
      </div>
    </a-drawer>

    <!-- 编辑抽屉 -->
    <a-drawer
      v-model:open="editDrawerVisible"
      width="520"
      title="编辑机房"
      placement="right"
    >
      <form
        class="drawer-form"
        @submit.prevent="saveEdit"
      >
        <label class="form-label">名称</label>
        <input
          v-model="editForm.name"
          class="form-input"
          required
        />
        <label class="form-label">位置</label>
        <input
          v-model="editForm.location"
          class="form-input"
          required
        />
        <label class="form-label">面积 (m²)</label>
        <input
          v-model.number="editForm.area"
          type="number"
          class="form-input"
          required
        />
        <label class="form-label">机柜数</label>
        <input
          v-model.number="editForm.rackCount"
          type="number"
          class="form-input"
          required
        />

        <div class="drawer-actions">
          <button
            type="button"
            class="btn secondary"
            @click="editDrawerVisible = false"
            >取消</button
          >
          <button
            type="submit"
            class="btn primary"
            >保存</button
          >
        </div>
      </form>
    </a-drawer>

    <!-- 新建机房对话框 -->
    <div
      v-if="showAddRoomDialog"
      class="modal-overlay"
      @click="showAddRoomDialog = false"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">新建机房</h2>
          <button
            class="modal-close"
            @click="showAddRoomDialog = false"
          >
            <XCircle class="close-icon" />
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="createRoom">
            <div class="form-group">
              <label class="form-label">机房名称</label>
              <input
                v-model="newRoom.name"
                type="text"
                class="form-input"
                placeholder="请输入机房名称"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">机房位置</label>
              <input
                v-model="newRoom.location"
                type="text"
                class="form-input"
                placeholder="请输入机房位置"
                required
              />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">面积 (m²)</label>
                <input
                  v-model.number="newRoom.area"
                  type="number"
                  class="form-input"
                  placeholder="面积"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机柜数量</label>
                <input
                  v-model.number="newRoom.rackCount"
                  type="number"
                  class="form-input"
                  placeholder="机柜数量"
                  required
                />
              </div>
            </div>
            <div class="modal-actions">
              <button
                type="button"
                class="btn btn-secondary"
                @click="showAddRoomDialog = false"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn btn-primary"
              >
                <Plus class="btn-icon" />
                创建机房
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Building,
  ChevronLeft,
  ChevronRight,
  Grid,
  Monitor,
  BarChart3,
  Settings,
  ArrowLeft,
  Filter,
  CheckCircle,
  Download,
  RefreshCw,
  Plus,
  AlertTriangle,
  XCircle,
  Thermometer,
  Droplets,
  Activity,
  Eye,
  Edit,
  Trash2,
  Layout
} from 'lucide-vue-next'

const router = useRouter()

// 进入机柜布局页面
const goRackLayout = (room: any) => {
  if (!room || !room.id) return
  try {
    // 可附带机柜数量，便于布局页初始化
    router.push({ name: 'RoomRackLayout', params: { roomId: room.id }, query: { rackCount: room.rackCount || 0 } })
  } catch (e) {
    console.error('跳转机柜布局失败', e)
  }
}

// 响应式数据
const sidebarExpanded = ref(true)
const loading = ref(false)
const batchMode = ref(false)
const showAddRoomDialog = ref(false)
const searchQuery = ref('')

// 详情/编辑抽屉状态
const detailDrawerVisible = ref(false)
const editDrawerVisible = ref(false)
const selectedRoom = ref<any | null>(null)

// 编辑表单
const editForm = ref({
  name: '',
  location: '',
  area: 0,
  rackCount: 0
})

// 新建机房表单数据
const newRoom = ref({
  name: '',
  location: '',
  area: 0,
  rackCount: 0
})

// 筛选器
const filters = ref({
  normal: true,
  warning: true,
  critical: true
})

// 机房数据
const rooms = ref([
  {
    id: 1,
    name: '主机房A',
    location: '北京数据中心 1F',
    area: 500,
    rackCount: 42,
    deviceCount: 156,
    status: 'normal',
    statusDetail: '运行正常',
    temperature: 22.5,
    humidity: 45,
    rackUtilization: 78,
    powerUtilization: 65,
    loading: false
  },
  {
    id: 2,
    name: '备用机房B',
    location: '北京数据中心 2F',
    area: 300,
    rackCount: 24,
    deviceCount: 89,
    status: 'warning',
    statusDetail: '温度偏高',
    temperature: 26.8,
    humidity: 52,
    rackUtilization: 45,
    powerUtilization: 38,
    loading: false
  },
  {
    id: 3,
    name: '存储机房C',
    location: '上海数据中心 1F',
    area: 400,
    rackCount: 36,
    deviceCount: 124,
    status: 'normal',
    statusDetail: '运行正常',
    temperature: 21.2,
    humidity: 48,
    rackUtilization: 82,
    powerUtilization: 71,
    loading: false
  }
])

// 计算属性
const totalRooms = computed(() => rooms.value.length)
const normalRooms = computed(() => rooms.value.filter((room) => room.status === 'normal').length)
const warningRooms = computed(() => rooms.value.filter((room) => room.status === 'warning').length)
const criticalRooms = computed(() => rooms.value.filter((room) => room.status === 'critical').length)

const filteredRooms = computed(() => {
  return rooms.value.filter((room) => {
    // 状态筛选
    if (!filters.value[room.status]) return false

    // 搜索筛选
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      return room.name.toLowerCase().includes(query) || room.location.toLowerCase().includes(query)
    }

    return true
  })
})

// 方法
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value
}

const toggleBatchMode = () => {
  batchMode.value = !batchMode.value
}

const refreshRooms = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const goBack = () => {
  // 返回企业资源管理页面
  router.push({ name: 'EnterpriseResources' })
}

/**
 * 跳转到机房总览
 */
const goOverview = () => {
  router.push({ name: 'RoomManagement' })
}

/**
 * 跳转到机房实时监控
 */
const goRealtimeMonitor = () => {
  router.push({ name: 'RoomRealtimeMonitor' })
}

/**
 * 跳转到机房数据分析
 */
const goDataAnalysis = () => {
  router.push({ name: 'RoomDataAnalysis' })
}

/**
 * 跳转到机房配置管理
 */
const goConfigManagement = () => {
  router.push({ name: 'RoomConfigManagement' })
}

const getStatusText = (status: string) => {
  const statusMap = {
    normal: '正常',
    warning: '告警',
    critical: '故障'
  }
  return statusMap[status] || '未知'
}

const viewRoomDetails = async (room: any) => {
  if (room.loading) return
  selectedRoom.value = { ...room }
  detailDrawerVisible.value = true
}

const editRoom = async (room: any) => {
  if (room.loading) return
  selectedRoom.value = { ...room }
  editForm.value = {
    name: room.name,
    location: room.location,
    area: room.area,
    rackCount: room.rackCount
  }
  editDrawerVisible.value = true
}

// 保存编辑
const saveEdit = () => {
  try {
    if (!selectedRoom.value) return
    const idx = rooms.value.findIndex((r) => r.id === selectedRoom.value!.id)
    if (idx !== -1) {
      rooms.value[idx] = {
        ...rooms.value[idx],
        name: editForm.value.name,
        location: editForm.value.location,
        area: editForm.value.area,
        rackCount: editForm.value.rackCount
      }
    }
    editDrawerVisible.value = false
  } catch (e) {
    console.error('保存编辑失败', e)
  }
}

const deleteRoom = (room: any) => {
  if (confirm(`确定要删除机房 "${room.name}" 吗？`)) {
    const index = rooms.value.findIndex((r) => r.id === room.id)
    if (index !== -1) {
      rooms.value.splice(index, 1)
    }
  }
}

// 新增的工具栏按钮事件处理方法
const toggleViewMode = () => {
  console.log('切换视图模式')
  // TODO: 实现视图模式切换逻辑
}

const toggleFilterPanel = () => {
  console.log('切换筛选面板')
  // TODO: 实现筛选面板切换逻辑
}

const exportRooms = () => {
  console.log('导出机房数据')
  // TODO: 实现导出功能
  const data = JSON.stringify(rooms.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'rooms-data.json'
  a.click()
  URL.revokeObjectURL(url)
}

// 创建新机房
const createRoom = () => {
  const room = {
    id: Date.now(),
    name: newRoom.value.name,
    location: newRoom.value.location,
    area: newRoom.value.area,
    rackCount: newRoom.value.rackCount,
    deviceCount: Math.floor(newRoom.value.rackCount * 0.7), // 假设70%的机柜有设备
    status: 'normal',
    statusDetail: '运行正常',
    temperature: Math.floor(Math.random() * 5) + 18,
    humidity: Math.floor(Math.random() * 20) + 40,
    rackUtilization: Math.floor(Math.random() * 30) + 60,
    powerUtilization: Math.floor(Math.random() * 40) + 50,
    loading: false
  }

  rooms.value.push(room)

  // 重置表单
  newRoom.value = {
    name: '',
    location: '',
    area: 0,
    rackCount: 0
  }

  // 关闭对话框
  showAddRoomDialog.value = false

  console.log('新建机房成功:', room)
}

onMounted(() => {
  refreshRooms()
})
</script>

<style scoped>
/* 基础布局优化 */
.room-management {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  position: relative;
}

.room-management::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 侧边栏样式优化 */
.sidebar {
  width: 300px;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 20;
  box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  /* 新增：固定侧边栏在 Flex 布局中的尺寸，避免被压缩导致不可见 */
  flex: 0 0 300px;
}

.sidebar.collapsed {
  width: 80px;
  /* 新增：收起时同步设置基础宽度，确保布局稳定 */
  flex-basis: 80px;
}

.sidebar-header {
  padding: 32px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
}

.brand {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brand-logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
  position: relative;
}

.brand-logo::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 18px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(8px);
}

.logo-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-title {
  font-size: 20px;
  font-weight: 800;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.sidebar-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
  width: 16px;
  height: 16px;
}

/* 导航样式 */
.sidebar-nav {
  padding: 20px 0;
  flex: 1;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-section-title {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 20px 12px;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 2px 12px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
}

/* 筛选器样式 */
.filter-group {
  padding: 0 20px;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  display: block;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 0;
}

.checkbox-item input[type='checkbox'] {
  display: none;
}

.checkbox-mark {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-item input[type='checkbox']:checked + .checkbox-mark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-item input[type='checkbox']:checked + .checkbox-mark::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-text {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

/* 主容器样式 */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 原：margin-left: 300px; 现在改为 0，避免产生额外留白 */
  margin-left: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.main-container.sidebar-collapsed {
  /* 原：margin-left: 80px; 现在与正常态一致，交由 Flex 自动分配 */
  margin-left: 0;
}

/* 页面头部样式优化 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 40px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  position: sticky;
  top: 0;
  z-index: 10;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.8);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: white;
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.5);
  width: 16px;
  height: 16px;
}

.breadcrumb-current {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
}

.breadcrumb-icon {
  width: 14px;
  height: 14px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 40px;
  position: relative;
  z-index: 1;
  width: 100%;
}

.title-group {
  flex: 1;
  min-width: 0;
}

.page-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 8px 0;
  letter-spacing: -1px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
  font-weight: 500;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;
  flex-shrink: 0;
  white-space: nowrap;
  justify-content: flex-end;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.toolbar-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: transparent;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-icon {
  width: 18px;
  height: 18px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
  background: white;
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(255, 255, 255, 0.3);
}

.btn-primary:active {
  transform: translateY(-1px);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 主要内容区域样式优化 */
.main-content {
  flex: 1;
  padding: 32px 40px;
  background: #f8fafc;
  overflow-y: auto;
}

/* 仪表板优化 */
.dashboard {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  margin-bottom: 48px;
}

.stats-overview {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  width: 100%;
}

/* 大屏幕布局 */
@media (min-width: 1400px) {
  .dashboard {
    grid-template-columns: 2fr 1fr;
    gap: 32px;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }
}

/* 中等屏幕布局 */
@media (min-width: 1200px) and (max-width: 1399px) {
  .dashboard {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .stats-overview {
    grid-template-columns: 2fr 1fr;
    gap: 24px;
  }
}

/* 小屏幕布局 */
@media (max-width: 1199px) {
  .stats-overview {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.overview-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.overview-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.overview-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.card-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.card-icon .icon {
  width: 32px;
  height: 32px;
  color: white;
}

.card-meta {
  text-align: right;
}

.trend-indicator {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
}

.trend-indicator.positive {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.trend-text {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 4px;
  display: block;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.primary-metric {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.metric-value {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.metric-unit {
  font-size: 18px;
  opacity: 0.8;
}

.metric-label {
  font-size: 16px;
  opacity: 0.9;
}

.metric-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  opacity: 0.8;
}

/* 状态统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.stat-card.success {
  border-left: 4px solid #10b981;
}

.stat-card.warning {
  border-left: 4px solid #f59e0b;
}

.stat-card.danger {
  border-left: 4px solid #ef4444;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-icon {
  width: 16px;
  height: 16px;
}

.stat-card.success .stat-icon {
  color: #10b981;
}

.stat-card.warning .stat-icon {
  color: #f59e0b;
}

.stat-card.danger .stat-icon {
  color: #ef4444;
}

.stat-trend {
  font-size: 10px;
  font-weight: 600;
}

.trend-value {
  color: #6b7280;
}

.stat-body {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.stat-detail {
  font-size: 10px;
  color: #9ca3af;
}

/* 监控面板样式 */
.monitoring-panel {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.panel-action {
  font-size: 14px;
  color: #3b82f6;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 6px 12px;
  border-radius: 6px;
}

.panel-action:hover {
  color: #1d4ed8;
  background: #f0f9ff;
}

.monitoring-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.monitor-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  min-height: 80px;
}

.monitor-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.monitor-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.monitor-icon.temperature {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.monitor-icon.humidity {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.monitor-icon.activity {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.monitor-icon .icon {
  width: 18px;
  height: 18px;
}

.monitor-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.monitor-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
  line-height: 1.2;
}

.monitor-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 2px;
  font-weight: 500;
}

.monitor-status {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.monitor-status.normal {
  background: #dcfce7;
  color: #166534;
}

/* 机房列表样式优化 */
.rooms-section {
  background: white;
  border-radius: 24px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  position: relative;
}

.rooms-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.room-count {
  font-size: 14px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 12px;
  border-radius: 20px;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  position: relative;
}

.search-input {
  width: 280px;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.filter-btn:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

.filter-btn .icon {
  width: 16px;
  height: 16px;
}

/* 表格样式 */
.rooms-table {
  overflow: hidden;
  width: 100%;
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: minmax(200px, 2fr) minmax(120px, 1fr) minmax(150px, 1.5fr) minmax(150px, 1.5fr) minmax(140px, 1.2fr);
  gap: 24px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  min-width: 820px;
}

.header-cell {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body {
  max-height: 600px;
  overflow-y: auto;
}

.room-row {
  display: grid;
  grid-template-columns: minmax(200px, 2fr) minmax(120px, 1fr) minmax(150px, 1.5fr) minmax(150px, 1.5fr) minmax(140px, 1.2fr);
  gap: 24px;
  padding: 28px 32px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 820px;
}

.room-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateX(4px);
}

.room-row:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.room-row.normal {
  border-left: 4px solid #10b981;
}

.room-row.warning {
  border-left: 4px solid #f59e0b;
}

.room-row.critical {
  border-left: 4px solid #ef4444;
}

.table-cell {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 机房信息列 */
.room-info-cell {
  gap: 16px;
}

.room-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.room-details {
  flex: 1;
}

.room-name {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 6px 0;
  letter-spacing: -0.3px;
}

.room-location {
  font-size: 15px;
  color: #6b7280;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.room-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #9ca3af;
}

.meta-separator {
  color: #d1d5db;
}

/* 状态列 */
.status-cell {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 24px;
  font-size: 13px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.normal {
  background: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.critical {
  background: #fee2e2;
  color: #991b1b;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-badge.normal .status-dot {
  background: #10b981;
}

.status-badge.warning .status-dot {
  background: #f59e0b;
}

.status-badge.critical .status-dot {
  background: #ef4444;
}

.status-detail {
  font-size: 11px;
  color: #9ca3af;
}

/* 环境监控列 */
.metrics-cell {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.metric-icon {
  width: 14px;
  height: 14px;
}

.metric-value {
  font-weight: 500;
  color: #374151;
}

/* 资源利用率列 */
.utilization-cell {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.utilization-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.utilization-label {
  font-size: 11px;
  color: #9ca3af;
  width: 32px;
  flex-shrink: 0;
}

.utilization-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.utilization-value {
  font-size: 11px;
  font-weight: 500;
  color: #374151;
  width: 32px;
  text-align: right;
  flex-shrink: 0;
}

/* 操作列 */
.actions-cell {
  justify-content: flex-end;
  min-width: 140px;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 加载状态样式 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.room-loading {
  opacity: 0.7;
  pointer-events: none;
}

.room-loading .room-row {
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 悬停效果增强 */
.room-row:not(.room-loading):hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.action-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  transform: scale(1.1);
}

.action-btn.danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 点击反馈 */
.btn:active:not(:disabled) {
  transform: translateY(1px);
}

.action-btn:active:not(:disabled) {
  transform: scale(0.95);
}

/* 统计网格样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  width: 100%;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.success::before {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card.warning::before {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.danger::before {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.1);
}

/* 响应式设计优化 */
@media (max-width: 1400px) {
  .stats-overview {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .dashboard {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 1200px) {
  .main-container {
    margin-left: 0;
  }

  .sidebar {
    position: fixed;
    left: -300px;
    z-index: 1000;
    transition: transform 0.3s ease;
  }

  .sidebar.collapsed {
    transform: translateX(300px);
  }

  .page-header {
    padding: 24px 32px;
  }

  .page-title {
    font-size: 28px;
  }

  .main-content {
    padding: 32px;
  }

  .toolbar {
    margin-left: auto;
    flex-wrap: nowrap; /* 强制一行展示 */
    justify-content: flex-end;
    gap: 8px; /* 小屏缩小间距，避免溢出 */
    overflow-x: auto; /* 不够放时允许横向滚动，但仍保持一行 */
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 16px;
  }

  .page-header {
    padding: 20px 24px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .title-section {
    flex-direction: row; /* 保持与大屏一致 */
    align-items: flex-end;
    gap: 16px;
  }

  .toolbar {
    flex-direction: row; /* 强制一行展示 */
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 8px; /* 收紧间距适配小屏 */
    overflow-x: auto; /* 不够放时允许横向滚动 */
  }

  .main-content {
    padding: 24px;
  }

  .dashboard {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .overview-card {
    padding: 20px;
    min-height: 160px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    min-height: 120px;
  }

  .monitoring-panel {
    padding: 20px;
    min-height: 250px;
  }

  .panel-title {
    font-size: 16px;
  }

  .monitoring-grid {
    gap: 12px;
  }

  .monitor-item {
    padding: 12px;
    min-height: 70px;
  }

  .monitor-icon {
    width: 36px;
    height: 36px;
  }

  .monitor-icon .icon {
    width: 16px;
    height: 16px;
  }

  .monitor-value {
    font-size: 18px;
  }

  .monitor-label {
    font-size: 12px;
  }

  .rooms-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-header,
  .room-row {
    min-width: 820px;
    grid-template-columns: 200px 120px 150px 150px 140px;
    gap: 16px;
    padding: 16px 20px;
  }

  .table-cell {
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .search-input {
    width: 200px;
  }

  .section-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px 20px;
  }

  .page-title {
    font-size: 20px;
  }

  .main-content {
    padding: 20px;
  }

  .overview-card {
    padding: 16px;
    min-height: 140px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    min-height: 100px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
  }

  .card-icon .icon {
    width: 24px;
    height: 24px;
  }

  .metric-value {
    font-size: 36px;
  }

  .monitoring-panel {
    padding: 16px;
    min-height: 200px;
  }

  .panel-title {
    font-size: 15px;
  }

  .monitoring-grid {
    gap: 10px;
  }

  .monitor-item {
    padding: 10px;
    min-height: 60px;
    gap: 12px;
  }

  .monitor-icon {
    width: 32px;
    height: 32px;
  }

  .monitor-icon .icon {
    width: 14px;
    height: 14px;
  }

  .monitor-value {
    font-size: 16px;
  }

  .monitor-label {
    font-size: 11px;
  }

  .monitor-status {
    font-size: 10px;
    padding: 1px 4px;
  }

  .search-input {
    width: 160px;
  }

  .section-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-header,
  .room-row {
    padding: 12px 16px;
  }

  .btn {
    padding: 8px 14px;
    font-size: 13px;
  }

  .toolbar-btn {
    width: 36px;
    height: 36px;
  }

  .toolbar-icon {
    width: 16px;
    height: 16px;
  }
}

/* 模态对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: #f3f4f6;
}

.close-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.modal-body {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition:
    border-color 0.2s,
    box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-secondary {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f3f4f6;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}
/* 修复：环境监控单元格文本被裁剪（如温度单位 °C 被截断） */
.table-cell.metrics-cell {
  overflow: visible; /* 允许内容溢出可见 */
  text-overflow: clip; /* 取消省略号 */
  white-space: normal; /* 允许换行，避免硬性截断 */
}
.metrics-cell .metric-item {
  min-width: 0; /* 允许在 flex 布局下收缩 */
}
.metrics-cell .metric-value {
  display: inline-flex;
  align-items: baseline;
  white-space: nowrap; /* 数值与单位同行显示，防止断行分离 */
}

/* 抽屉内部样式 */
.drawer-body {
  padding: 8px 0;
}
.drawer-row {
  margin: 8px 0;
  color: #334155;
}
.drawer-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.drawer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}
.btn.primary {
  background: #4f46e5;
  color: #fff;
  border-radius: 6px;
  padding: 6px 12px;
}
.btn.secondary {
  background: #e2e8f0;
  color: #0f172a;
  border-radius: 6px;
  padding: 6px 12px;
}
</style>
